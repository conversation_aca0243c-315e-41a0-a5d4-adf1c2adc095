#!/usr/bin/env python3
"""
生肖运势生成器菜单
提供友好的交互界面来选择不同的功能
"""

import os
import sys
from datetime import datetime

def show_menu():
    """显示主菜单"""
    print("=" * 60)
    print("🔮 生肖今日运势自动生成器")
    print("=" * 60)
    print(f"📅 当前日期: {datetime.now().strftime('%Y年%m月%d日')}")
    print(f"🌐 目标站点: https://yi958.com")
    print()
    print("请选择功能:")
    print()
    print("1. 生成单个生肖运势")
    print("2. 生成所有12生肖运势")
    print("3. 批量生成（测试模式 - 前3个生肖）")
    print("4. 测试OpenAI API连接")
    print("5. 测试WordPress发布功能")
    print("6. 查看使用说明")
    print("7. 配置管理")
    print("8. 调度器管理")
    print("0. 退出")
    print()

def show_zodiac_menu():
    """显示生肖选择菜单"""
    zodiacs = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
    
    print("\n请选择生肖:")
    for i, zodiac in enumerate(zodiacs, 1):
        print(f"{i:2d}. {zodiac}")
    print(" 0. 返回主菜单")
    
    while True:
        try:
            choice = input("\n请输入选择 (0-12): ").strip()
            if choice == '0':
                return None
            
            choice_num = int(choice)
            if 1 <= choice_num <= 12:
                return zodiacs[choice_num - 1]
            else:
                print("❌ 请输入有效的数字 (0-12)")
        except ValueError:
            print("❌ 请输入有效的数字")

def run_command(command):
    """运行命令并显示结果"""
    print(f"\n🚀 执行命令: {command}")
    print("-" * 50)
    
    result = os.system(command)
    
    print("-" * 50)
    if result == 0:
        print("✅ 命令执行成功")
    else:
        print("❌ 命令执行失败")
    
    input("\n按回车键继续...")

def show_help():
    """显示帮助信息"""
    print("\n" + "=" * 60)
    print("📖 使用说明")
    print("=" * 60)
    print()
    print("🎯 功能说明:")
    print("• 单个生肖运势: 生成指定生肖的今日运势并发布")
    print("• 所有生肖运势: 生成全部12个生肖的运势并发布")
    print("• 测试模式: 只生成前3个生肖，用于测试")
    print("• API测试: 测试OpenAI API是否正常工作")
    print("• 发布测试: 测试WordPress发布功能")
    print()
    print("⚙️ 配置信息:")
    print("• OpenAI API: https://api.leaflow.net/v1")
    print("• 模型: gpt-4o-mini")
    print("• WordPress站点: https://yi958.com")
    print("• 分类: 生肖今日运势")
    print()
    print("📝 生成内容包含:")
    print("• 标题: 生肖属[X]人今日运势运程财运吉凶（日期）详解查询")
    print("• 固定导语")
    print("• 四大运势: 爱情运、事业运、财运、人际运")
    print("• 开运指南: 开运数字、开运颜色、开运建议")
    print()
    print("⚠️ 注意事项:")
    print("• 每次生成会消耗OpenAI API额度")
    print("• 生成的文章会直接发布到WordPress站点")
    print("• 建议先使用测试功能确认配置正确")
    print()
    
    input("按回车键返回主菜单...")

def main():
    """主函数"""
    while True:
        show_menu()
        
        choice = input("请输入选择 (0-8): ").strip()
        
        if choice == '0':
            print("\n👋 再见！")
            break
            
        elif choice == '1':
            # 生成单个生肖运势
            zodiac = show_zodiac_menu()
            if zodiac:
                run_command(f"python generate_daily_post.py {zodiac}")
                
        elif choice == '2':
            # 生成所有生肖运势
            print("\n⚠️ 警告: 这将生成所有12个生肖的运势，可能需要较长时间")
            confirm = input("确认继续？(y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                run_command("python generate_all_zodiacs.py")
                
        elif choice == '3':
            # 测试模式
            print("\n🧪 测试模式: 将生成前3个生肖的运势")
            confirm = input("确认继续？(y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                run_command("python generate_all_zodiacs.py --test")
                
        elif choice == '4':
            # 测试API
            run_command("python test_generate.py")
            
        elif choice == '5':
            # 测试发布
            print("\n⚠️ 警告: 这将生成并发布一篇测试文章")
            confirm = input("确认继续？(y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                run_command("python test_publish.py")
                
        elif choice == '6':
            # 查看帮助
            show_help()

        elif choice == '7':
            # 配置管理
            run_command("python config_manager.py")

        elif choice == '8':
            # 调度器管理
            print("\n📋 调度器管理选项:")
            print("1. 查看任务列表")
            print("2. 立即执行生肖运势任务")
            print("3. 启动调度器")

            sub_choice = input("请选择 (1-3): ").strip()
            if sub_choice == '1':
                run_command("python scheduler.py list")
            elif sub_choice == '2':
                run_command("python scheduler.py run zodiac_fortune")
            elif sub_choice == '3':
                print("⚠️ 调度器将在前台运行，按Ctrl+C停止")
                confirm = input("确认启动？(y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    run_command("python scheduler.py start")

        else:
            print("❌ 无效选择，请重新输入")
            input("按回车键继续...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        input("按回车键退出...")
