# 🚀 快速开始指南

## 一分钟上手

### 1. 启动交互式菜单
```bash
python menu.py
```

### 2. 选择功能
- **选择 4**: 测试OpenAI API连接
- **选择 5**: 测试WordPress发布功能  
- **选择 1**: 生成单个生肖运势
- **选择 2**: 生成所有12生肖运势

### 3. 完成！
生成的文章会自动发布到 https://yi958.com 的"生肖今日运势"分类中。

## 命令行快速使用

### 生成单个生肖
```bash
python generate_daily_post.py 牛
```

### 生成所有生肖
```bash
python generate_all_zodiacs.py
```

### 测试模式（前3个生肖）
```bash
python generate_all_zodiacs.py --test
```

### 自动化运行（每天08:00执行）
```bash
# 启动调度器
python scheduler.py start

# 安装为系统服务
python setup_service.py
```

## 配置信息

✅ **已配置完成，无需修改**

- OpenAI API: `https://api.leaflow.net/v1`
- API Key: `sk-Abwb3yNpXXbJiMFmD3Ca2a3b12C846AdAaF2C5A0Bc16Ef08`
- WordPress站点: `https://yi958.com`
- 模型: `gpt-4o-mini`

## 生成内容示例

**标题**: 生肖属牛人今日运势运程财运吉凶（2025年07月03日）详解查询

**内容包含**:
- 固定导语
- 今日爱情运
- 今日事业运  
- 今日财运
- 今日人际运
- 开运数字、颜色、建议

## 支持的生肖

🐭 鼠 | 🐮 牛 | 🐯 虎 | 🐰 兔 | 🐲 龙 | 🐍 蛇
🐴 马 | 🐑 羊 | 🐵 猴 | 🐔 鸡 | 🐶 狗 | 🐷 猪

## 常见问题

**Q: SSL警告怎么办？**
A: 这是正常的，脚本已处理SSL问题，不影响使用。

**Q: 生成失败怎么办？**
A: 检查网络连接，或单独重新生成失败的生肖。

**Q: 如何查看生成的文章？**
A: 脚本会输出文章链接，如: https://yi958.com/mingyun/46532

## 需要帮助？

查看详细文档: `README_generate_daily_post.md`
