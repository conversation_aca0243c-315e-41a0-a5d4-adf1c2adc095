#!/usr/bin/env python3
"""
自动化任务调度器
管理所有定时任务的执行
"""

import schedule
import time
import subprocess
import logging
from datetime import datetime
import os
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class TaskScheduler:
    """任务调度器类"""
    
    def __init__(self):
        self.tasks = {}
        
    def add_task(self, name, command, schedule_time, description=""):
        """
        添加定时任务
        
        Args:
            name: 任务名称
            command: 要执行的命令
            schedule_time: 执行时间 (格式: "HH:MM")
            description: 任务描述
        """
        self.tasks[name] = {
            'command': command,
            'schedule_time': schedule_time,
            'description': description,
            'last_run': None,
            'status': 'waiting'
        }
        
        # 注册到schedule
        schedule.every().day.at(schedule_time).do(self._run_task, name)
        logger.info(f"已添加任务: {name} - {schedule_time} - {description}")
    
    def _run_task(self, task_name):
        """执行指定任务"""
        if task_name not in self.tasks:
            logger.error(f"任务不存在: {task_name}")
            return
        
        task = self.tasks[task_name]
        logger.info(f"开始执行任务: {task_name}")
        
        try:
            task['status'] = 'running'
            task['last_run'] = datetime.now()
            
            # 执行命令
            result = subprocess.run(
                task['command'],
                shell=True,
                capture_output=True,
                text=True,
                timeout=3600  # 1小时超时
            )
            
            if result.returncode == 0:
                task['status'] = 'completed'
                logger.info(f"任务执行成功: {task_name}")
                logger.info(f"输出: {result.stdout}")
            else:
                task['status'] = 'failed'
                logger.error(f"任务执行失败: {task_name}")
                logger.error(f"错误: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            task['status'] = 'timeout'
            logger.error(f"任务执行超时: {task_name}")
        except Exception as e:
            task['status'] = 'error'
            logger.error(f"任务执行异常: {task_name} - {e}")
        
        # 重置状态为等待下次执行
        task['status'] = 'waiting'
    
    def list_tasks(self):
        """列出所有任务"""
        print("\n" + "="*80)
        print("📅 定时任务列表")
        print("="*80)
        
        if not self.tasks:
            print("暂无定时任务")
            return
        
        for name, task in self.tasks.items():
            print(f"🔹 {name}")
            print(f"   时间: {task['schedule_time']}")
            print(f"   命令: {task['command']}")
            print(f"   描述: {task['description']}")
            print(f"   状态: {task['status']}")
            if task['last_run']:
                print(f"   上次执行: {task['last_run'].strftime('%Y-%m-%d %H:%M:%S')}")
            print()
    
    def run_task_now(self, task_name):
        """立即执行指定任务"""
        if task_name not in self.tasks:
            logger.error(f"任务不存在: {task_name}")
            return False
        
        logger.info(f"手动执行任务: {task_name}")
        self._run_task(task_name)
        return True
    
    def start(self):
        """启动调度器"""
        logger.info("任务调度器启动")
        self.list_tasks()
        
        print(f"\n⏰ 调度器已启动，当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("按 Ctrl+C 停止调度器")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            logger.info("调度器已停止")
            print("\n👋 调度器已停止")

def setup_default_tasks():
    """设置默认任务"""
    scheduler = TaskScheduler()
    
    # 生肖运势任务 - 每天早上8点执行
    scheduler.add_task(
        name="zodiac_fortune",
        command="python generate_all_zodiacs.py --auto",
        schedule_time="08:00",
        description="生成12生肖今日运势并发布到WordPress"
    )
    
    # 可以添加更多任务
    # scheduler.add_task(
    #     name="other_task",
    #     command="python other_script.py",
    #     schedule_time="10:00",
    #     description="其他定时任务"
    # )
    
    return scheduler

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "list":
            # 列出任务
            scheduler = setup_default_tasks()
            scheduler.list_tasks()
            
        elif command == "run":
            # 立即执行指定任务
            if len(sys.argv) < 3:
                print("用法: python scheduler.py run <task_name>")
                return
            
            task_name = sys.argv[2]
            scheduler = setup_default_tasks()
            scheduler.run_task_now(task_name)
            
        elif command == "start":
            # 启动调度器
            scheduler = setup_default_tasks()
            scheduler.start()
            
        else:
            print("未知命令")
            print_usage()
    else:
        print_usage()

def print_usage():
    """打印使用说明"""
    print("📋 任务调度器使用说明")
    print("="*50)
    print("python scheduler.py start          # 启动调度器")
    print("python scheduler.py list           # 列出所有任务")
    print("python scheduler.py run <task>     # 立即执行指定任务")
    print()
    print("示例:")
    print("python scheduler.py start")
    print("python scheduler.py run zodiac_fortune")

if __name__ == "__main__":
    main()
