#!/usr/bin/env python3
"""
配置管理模块
集中管理所有配置信息，包括OpenAI和WordPress配置
"""

from openai import OpenAI
from typing import Optional, Dict, Any
import threading


class Config:
    """配置管理类 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(Config, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        
        # OpenAI API 配置
        self.OPENAI_CONFIG = {
            "api_key": "sk-Abwb3yNpXXbJiMFmD3Ca2a3b12C846AdAaF2C5A0Bc16Ef08",
            "base_url": "https://api.leaflow.net/v1",
            "model": "gpt-4o-mini",
            "temperature": 0.7,
            "max_tokens": 1500,
            "timeout": 60
        }
        
        # WordPress API 配置
        self.WORDPRESS_CONFIG = {
            "api_key": "Z6oB UFjS 25Lp LkZW yg9p dI9O",
            "username": "admin",
            "timeout": 60,
            "verify_ssl": False,
            "category_name": "生肖今日运势"
        }
        
        # WordPress 站点配置
        self.WORDPRESS_SITES = {
            'www': "https://yi958.com",
            'xz': "https://xz.yi958.com",
            'zb': "https://zb.yi958.com",
            'bz': "https://bz.yi958.com",
            'xm': "https://xm.yi958.com",
            'ziwei': "https://ziwei.yi958.com",
            'sxmx': "https://sxmx.yi958.com",
            'qimen': "https://qimen.yi958.com",
            'ms': "https://ms.yi958.com",
            'xh': "https://xh.yi958.com",
            'fs': "https://fs.yi958.com",
            'jm': "https://jm.yi958.com",
        }
        
        # 生肖配置
        self.ZODIAC_CONFIG = {
            "animals": ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"],
            "batch_delay": 5,  # 批量生成时每个生肖之间的延迟（秒）
            "retry_count": 3,  # 失败重试次数
            "retry_delay": 2   # 重试延迟（秒）
        }
        
        # 调度器配置
        self.SCHEDULER_CONFIG = {
            "zodiac_fortune_time": "08:00",  # 生肖运势生成时间
            "log_file": "scheduler.log",
            "error_log_file": "scheduler.error.log",
            "task_timeout": 3600  # 任务超时时间（秒）
        }
        
        # 内容模板配置
        self.CONTENT_CONFIG = {
            "title_template": "生肖属{zodiac}人今日运势运程财运吉凶（{date}）详解查询",
            "intro_template": "以下是生肖属{zodiac}的人{date}的运势预测。易德轩提醒您，记得每天来查看十二生肖今日运程每日运势，大师帮你分析事业、爱情、财运，助您掌握自身运势每日过得精彩。请收藏[易德轩每日十二生肖属相运程]为书签网址，方便下次点击即可直达。",
            "ending_text": """以上是根据你今天的生肖运势所提供的今日幸运颜色、数字、贵人等开运方法，可以让你的自信心加倍，好运能量上升！
　　生肖，即十二属相，指不同年份出生的人，具有先天不同的属相命运，十二年一个轮回，此为"命"也。属相的先天命运，随着日月轮回的变化，每天的运势也不一样，此为"运"也，所谓"命运"，就是指先天的命相和后天的运程。
　　十二生肖每日运势在线查询，即根据生肖五行相生相克、生肖地支之间相刑相冲以及命理神煞学，来解析十二属相的每日运势，旨在为您今天的运势提供一些参考。此外，我们结合生肖运势，提供了一些开运建议。"""
        }
        
        # OpenAI 客户端实例（延迟初始化）
        self._openai_client = None
        self._openai_client_lock = threading.Lock()
        
        self._initialized = True
    
    @property
    def openai_client(self) -> OpenAI:
        """获取OpenAI客户端实例（单例模式）"""
        if self._openai_client is None:
            with self._openai_client_lock:
                if self._openai_client is None:
                    self._openai_client = OpenAI(
                        api_key=self.OPENAI_CONFIG["api_key"],
                        base_url=self.OPENAI_CONFIG["base_url"]
                    )
        return self._openai_client
    
    def get_openai_config(self) -> Dict[str, Any]:
        """获取OpenAI配置"""
        return self.OPENAI_CONFIG.copy()
    
    def get_wordpress_config(self) -> Dict[str, Any]:
        """获取WordPress配置"""
        return self.WORDPRESS_CONFIG.copy()
    
    def get_wordpress_sites(self) -> Dict[str, str]:
        """获取WordPress站点配置"""
        return self.WORDPRESS_SITES.copy()
    
    def get_wordpress_site_url(self, site_key: str = 'www') -> Optional[str]:
        """获取指定站点URL"""
        return self.WORDPRESS_SITES.get(site_key)
    
    def get_zodiac_animals(self) -> list:
        """获取生肖列表"""
        return self.ZODIAC_CONFIG["animals"].copy()
    
    def get_zodiac_config(self) -> Dict[str, Any]:
        """获取生肖配置"""
        return self.ZODIAC_CONFIG.copy()
    
    def get_scheduler_config(self) -> Dict[str, Any]:
        """获取调度器配置"""
        return self.SCHEDULER_CONFIG.copy()
    
    def get_content_config(self) -> Dict[str, Any]:
        """获取内容配置"""
        return self.CONTENT_CONFIG.copy()
    
    def get_wordpress_auth(self) -> tuple:
        """获取WordPress认证信息"""
        return (
            self.WORDPRESS_CONFIG["username"],
            self.WORDPRESS_CONFIG["api_key"].replace(' ', '')
        )
    
    def update_openai_config(self, **kwargs):
        """更新OpenAI配置"""
        for key, value in kwargs.items():
            if key in self.OPENAI_CONFIG:
                self.OPENAI_CONFIG[key] = value
        
        # 重置客户端实例，下次访问时重新创建
        with self._openai_client_lock:
            self._openai_client = None
    
    def update_wordpress_config(self, **kwargs):
        """更新WordPress配置"""
        for key, value in kwargs.items():
            if key in self.WORDPRESS_CONFIG:
                self.WORDPRESS_CONFIG[key] = value
    
    def add_wordpress_site(self, key: str, url: str):
        """添加WordPress站点"""
        self.WORDPRESS_SITES[key] = url
    
    def remove_wordpress_site(self, key: str):
        """移除WordPress站点"""
        if key in self.WORDPRESS_SITES:
            del self.WORDPRESS_SITES[key]
    
    def update_scheduler_time(self, task_name: str, time: str):
        """更新调度时间"""
        if task_name == "zodiac_fortune":
            self.SCHEDULER_CONFIG["zodiac_fortune_time"] = time
    
    def get_prompt_template(self) -> str:
        """获取AI提示词模板"""
        return """你是一位资深命理专家，请根据给定的日期和生肖生成今日运势报告。

请只输出文章正文部分，不要包含标题和导语。

运势分类（每类2-3句话）： 
今日爱情运：[生成描述] 
今日事业运：[生成描述] 
今日财运：[生成描述] 
今日人际运：[生成描述]

开运指南（严格按顺序）： 
开运数字：[主推2个数字]；最佳，其次[补充2个数字] 
开运颜色：[2种颜色] 
开运建议：[1-2句指导性话语，包含"学会"等动词]

生成规则：
- 日期格式：YYYY年MM月DD日
- 用口语化中文，避免极端负面词汇
- 财运描述需包含风险提示
- 开运数字颜色需符合生肖五行
- 爱情运需区分未婚/已婚状态
- 事业运要包含具体建议

现在请为日期={date} 生肖={zodiac} 生成运势正文内容。只输出正文，不要标题和导语。"""
    
    def format_article_content(self, zodiac: str, date: str, content: str) -> str:
        """格式化完整文章内容"""
        title = self.CONTENT_CONFIG["title_template"].format(zodiac=zodiac, date=date)
        intro = self.CONTENT_CONFIG["intro_template"].format(zodiac=zodiac, date=date)
        ending = self.CONTENT_CONFIG["ending_text"]
        
        return f"「{title}」\n\n{intro}\n\n{content}\n\n{ending}"
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Config(OpenAI: {self.OPENAI_CONFIG['base_url']}, WordPress: {len(self.WORDPRESS_SITES)} sites)"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()


# 全局配置实例
config = Config()

# 便捷访问函数
def get_config() -> Config:
    """获取配置实例"""
    return config

def get_openai_client() -> OpenAI:
    """获取OpenAI客户端"""
    return config.openai_client
