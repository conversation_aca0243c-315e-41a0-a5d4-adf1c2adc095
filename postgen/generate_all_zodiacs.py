#!/usr/bin/env python3
"""
批量生成所有12生肖今日运势
"""

import time
from generate_daily_post import main as generate_main, ZODIAC_ANIMALS
from datetime import datetime

def main():
    """
    批量生成所有12生肖的运势，每个生肖之间间隔一定时间
    """
    import sys

    print("=" * 60)
    print("批量生成所有12生肖今日运势")
    print("=" * 60)

    today = datetime.now()
    date_str = today.strftime("%Y年%m月%d日")

    # 检查命令行参数
    test_mode = len(sys.argv) > 1 and '--test' in sys.argv
    auto_mode = len(sys.argv) > 1 and '--auto' in sys.argv

    target_zodiacs = ZODIAC_ANIMALS[:3] if test_mode else ZODIAC_ANIMALS

    print(f"日期: {date_str}")
    if test_mode:
        print(f"模式: 测试模式 (前3个生肖)")
    elif auto_mode:
        print(f"模式: 自动模式 (所有12个生肖)")
    else:
        print(f"模式: 完整模式 (所有12个生肖)")

    print(f"将生成 {len(target_zodiacs)} 个生肖的运势")
    print(f"生肖列表: {', '.join(target_zodiacs)}")

    # 自动模式不需要确认
    if not auto_mode:
        confirm = input("\n是否继续？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("操作已取消")
            return
    else:
        print("\n自动模式启动，开始执行...")
    
    print("\n开始批量生成...")
    
    success_count = 0
    failed_zodiacs = []

    for i, zodiac in enumerate(target_zodiacs, 1):
        print(f"\n[{i}/{len(target_zodiacs)}] 正在处理生肖：{zodiac}")
        print("-" * 40)

        try:
            # 调用主生成函数，传入生肖参数
            import sys
            original_argv = sys.argv.copy()
            sys.argv = ['generate_daily_post.py', zodiac]

            generate_main()

            # 恢复原始参数
            sys.argv = original_argv

            success_count += 1
            print(f"✅ {zodiac} 处理完成")

            # 如果不是最后一个，等待一段时间避免API限制
            if i < len(target_zodiacs):
                print("等待5秒后继续...")
                time.sleep(5)
                
        except Exception as e:
            print(f"❌ {zodiac} 处理失败: {e}")
            failed_zodiacs.append(zodiac)
            
            # 即使失败也等待一下
            if i < len(target_zodiacs):
                print("等待3秒后继续...")
                time.sleep(3)

    # 输出最终结果
    print("\n" + "=" * 60)
    print("批量生成完成！")
    print("=" * 60)
    print(f"总计: {len(target_zodiacs)} 个生肖")
    print(f"成功: {success_count} 个")
    print(f"失败: {len(failed_zodiacs)} 个")
    
    if failed_zodiacs:
        print(f"失败的生肖: {', '.join(failed_zodiacs)}")
        print("\n可以单独重新生成失败的生肖:")
        for zodiac in failed_zodiacs:
            print(f"  python generate_daily_post.py {zodiac}")
    
    print(f"\n生成完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
