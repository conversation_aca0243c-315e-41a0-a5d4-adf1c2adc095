# 生肖今日运势自动生成器

这个脚本可以自动生成12生肖的今日运势文章，并发布到WordPress站点。

## 功能特点

- 使用OpenAI GPT-4o-mini模型生成高质量的运势内容
- 支持生成所有12生肖的运势
- 自动发布到WordPress站点的"生肖今日运势"分类
- 支持单个生肖或全部生肖的生成
- 包含完整的错误处理和重试机制

## 配置信息

### OpenAI API配置
- **API Key**: `sk-Abwb3yNpXXbJiMFmD3Ca2a3b12C846AdAaF2C5A0Bc16Ef08`
- **Base URL**: `https://api.leaflow.net/v1`
- **模型**: `gpt-4o-mini`

### WordPress站点配置
- **目标站点**: `https://yi958.com` (www站点)
- **分类**: "生肖今日运势"
- **API认证**: 使用sites.py中的配置

## 使用方法

### 0. 交互式菜单（推荐）
```bash
python menu.py
```
提供友好的交互界面，可以选择不同的功能。

### 1. 生成所有12生肖运势
```bash
python generate_daily_post.py
```

### 2. 生成指定生肖运势
```bash
python generate_daily_post.py 牛
python generate_daily_post.py 龙
python generate_daily_post.py 鼠
```

### 3. 批量生成所有生肖运势
```bash
# 生成所有12个生肖
python generate_all_zodiacs.py

# 测试模式（只生成前3个生肖）
python generate_all_zodiacs.py --test

# 自动模式（无需确认，用于定时任务）
python generate_all_zodiacs.py --auto
```

### 4. 自动化调度
```bash
# 查看定时任务
python scheduler.py list

# 立即执行指定任务
python scheduler.py run zodiac_fortune

# 启动调度器（每天08:00自动执行）
python scheduler.py start

# 安装为系统服务
python setup_service.py
```

支持的生肖：鼠、牛、虎、兔、龙、蛇、马、羊、猴、鸡、狗、猪

## 生成内容格式

每篇运势文章包含以下内容：

### 标题格式
```
生肖属[生肖]人今日运势运程财运吉凶（[日期]）详解查询
```

### 固定导语
```
以下是生肖属[生肖]的人[日期]的运势预测。易德轩提醒您，记得每天来查看十二生肖今日运程每日运势，大师帮你分析事业、爱情、财运，助您掌握自身运势每日过得精彩。请收藏[易德轩每日十二生肖属相运程]为书签网址，方便下次点击即可直达。
```

### 固定结尾
```
以上是根据你今天的生肖运势所提供的今日幸运颜色、数字、贵人等开运方法，可以让你的自信心加倍，好运能量上升！
　　生肖，即十二属相，指不同年份出生的人，具有先天不同的属相命运，十二年一个轮回，此为"命"也。属相的先天命运，随着日月轮回的变化，每天的运势也不一样，此为"运"也，所谓"命运"，就是指先天的命相和后天的运程。
　　十二生肖每日运势在线查询，即根据生肖五行相生相克、生肖地支之间相刑相冲以及命理神煞学，来解析十二属相的每日运势，旨在为您今天的运势提供一些参考。此外，我们结合生肖运势，提供了一些开运建议。
```

### 运势分类
- **今日爱情运**：2-3句话，区分未婚/已婚状态
- **今日事业运**：2-3句话，包含具体建议
- **今日财运**：2-3句话，包含风险提示
- **今日人际运**：2-3句话

### 开运指南
- **开运数字**：主推2个数字；最佳，其次补充2个数字
- **开运颜色**：2种颜色，符合生肖五行
- **开运建议**：1-2句指导性话语，包含"学会"等动词

## 测试脚本

### test_generate.py
测试OpenAI API生成功能，生成单个生肖运势内容但不发布。

```bash
python test_generate.py
```

### test_publish.py
测试完整的生成和发布流程，生成并发布一篇牛的运势。

```bash
python test_publish.py
```

### generate_all_zodiacs.py
批量生成所有12生肖的运势，支持测试模式。

```bash
# 完整模式
python generate_all_zodiacs.py

# 测试模式（前3个生肖）
python generate_all_zodiacs.py --test
```

## 注意事项

1. **SSL警告**: 脚本会显示SSL警告，这是正常的，因为使用了`verify=False`来避免SSL证书问题。

2. **分类创建**: 如果"生肖今日运势"分类不存在，脚本会尝试自动创建。如果创建失败，文章仍会发布但不会设置分类。

3. **错误处理**: 脚本包含完整的错误处理，如果某个生肖的运势生成或发布失败，会跳过并继续处理下一个。

4. **日期格式**: 自动使用当前日期，格式为"YYYY年MM月DD日"。

## 输出示例

```
脚本开始执行...
只生成指定生肖：龙
开始生成2025年07月03日的生肖运势...
目标生肖：龙
目标站点：https://yi958.com
找到现有分类: 生肖今日运势 (ID: 123)

正在处理生肖：龙
  正在生成龙运势内容...
  ✅ 龙运势内容生成成功
  正在发布龙运势到WordPress...
  ✅ 龙运势发布成功
     文章ID: 46532
     文章链接: https://yi958.com/mingyun/46532

任务完成！成功发布 1/1 篇文章
```

## 自动化部署

### 1. 设置定时任务
```bash
# 启动调度器（前台运行）
python scheduler.py start

# 安装为系统服务（后台运行）
python setup_service.py
```

### 2. 调度配置
- **执行时间**: 每天08:00
- **任务内容**: 自动生成12生肖运势并发布
- **日志文件**: scheduler.log

### 3. 添加其他定时任务
编辑 `scheduler.py` 中的 `setup_default_tasks()` 函数：
```python
# 添加新任务
scheduler.add_task(
    name="other_task",
    command="python other_script.py",
    schedule_time="10:00",
    description="其他定时任务描述"
)
```

## 依赖包

- `openai>=1.93.0`
- `requests>=2.32.4`
- `schedule>=1.2.2`

## 文件结构

```
postgen/
├── menu.py                   # 🌟 交互式菜单（推荐使用）
├── generate_daily_post.py    # 主脚本
├── generate_all_zodiacs.py   # 批量生成脚本
├── scheduler.py              # 🤖 自动化调度器
├── setup_service.py          # 系统服务安装脚本
├── test_generate.py          # 测试生成功能
├── test_publish.py           # 测试发布功能
├── sites.py                  # WordPress站点配置
├── QUICKSTART.md             # 快速开始指南
├── scheduler.log             # 调度器日志文件
└── README_generate_daily_post.md  # 详细使用说明
```
