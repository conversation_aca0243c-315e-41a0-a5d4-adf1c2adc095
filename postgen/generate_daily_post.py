#!/usr/bin/env python3
"""
生肖今日运势自动生成器
自动生成12生肖的今日运势文章并发布到WordPress站点
"""

import os
import json
import requests
from datetime import datetime
from openai import OpenAI
from sites import sites, _WP_API_KEY, _WP_USER

# OpenAI 配置
OPENAI_API_KEY = "sk-Abwb3yNpXXbJiMFmD3Ca2a3b12C846AdAaF2C5A0Bc16Ef08"
OPENAI_BASE_URL = "https://api.leaflow.net/v1"  # 添加 /v1 路径
MODEL = "gpt-4o-mini"

# 12生肖列表
ZODIAC_ANIMALS = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]

# 初始化OpenAI客户端
client = OpenAI(
    api_key=OPENAI_API_KEY,
    base_url=OPENAI_BASE_URL
)

def generate_fortune_content(date_str, zodiac):
    """
    使用OpenAI生成指定生肖的运势内容
    """
    prompt = f"""你是一位资深命理专家，请根据给定的日期和生肖生成今日运势报告。

请只输出文章正文部分，不要包含标题和导语。

运势分类（每类2-3句话）：
今日爱情运：[生成描述]
今日事业运：[生成描述]
今日财运：[生成描述]
今日人际运：[生成描述]

开运指南（严格按顺序）：
开运数字：[主推2个数字]；最佳，其次[补充2个数字]
开运颜色：[2种颜色]
开运建议：[1-2句指导性话语，包含"学会"等动词]

生成规则：
- 日期格式：YYYY年MM月DD日
- 用口语化中文，避免极端负面词汇
- 财运描述需包含风险提示
- 开运数字颜色需符合生肖五行
- 爱情运需区分未婚/已婚状态
- 事业运要包含具体建议

现在请为日期={date_str} 生肖={zodiac} 生成运势正文内容。只输出正文，不要标题和导语。"""

    try:
        response = client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=1500
        )

        # 获取生成的正文内容
        content = response.choices[0].message.content.strip()

        # 构建完整的文章内容
        title = f"生肖属{zodiac}人今日运势运程财运吉凶（{date_str}）详解查询"

        intro = f"以下是生肖属{zodiac}的人{date_str}的运势预测。易德轩提醒您，记得每天来查看十二生肖今日运程每日运势，大师帮你分析事业、爱情、财运，助您掌握自身运势每日过得精彩。请收藏[易德轩每日十二生肖属相运程]为书签网址，方便下次点击即可直达。"

        ending = """以上是根据你今天的生肖运势所提供的今日幸运颜色、数字、贵人等开运方法，可以让你的自信心加倍，好运能量上升！
　　生肖，即十二属相，指不同年份出生的人，具有先天不同的属相命运，十二年一个轮回，此为"命"也。属相的先天命运，随着日月轮回的变化，每天的运势也不一样，此为"运"也，所谓"命运"，就是指先天的命相和后天的运程。
　　十二生肖每日运势在线查询，即根据生肖五行相生相克、生肖地支之间相刑相冲以及命理神煞学，来解析十二属相的每日运势，旨在为您今天的运势提供一些参考。此外，我们结合生肖运势，提供了一些开运建议。"""

        # 组合完整内容
        full_content = f"「{title}」\n\n{intro}\n\n{content}\n\n{ending}"

        return full_content

    except Exception as e:
        print(f"生成{zodiac}运势时出错: {e}")
        return None

def get_category_id(site_url, category_name):
    """
    获取WordPress分类ID，如果不存在则创建
    """
    # 获取现有分类
    categories_url = f"{site_url}/wp-json/wp/v2/categories"

    try:
        # 添加SSL验证和超时设置
        response = requests.get(categories_url, verify=False, timeout=30)
        if response.status_code == 200:
            categories = response.json()
            for category in categories:
                if category['name'] == category_name:
                    print(f"找到现有分类: {category_name} (ID: {category['id']})")
                    return category['id']

        # 如果分类不存在，创建新分类
        print(f"分类 '{category_name}' 不存在，正在创建...")
        create_data = {
            'name': category_name,
            'description': f'{category_name}相关文章'
        }

        auth = (_WP_USER, _WP_API_KEY.replace(' ', ''))
        response = requests.post(categories_url, json=create_data, auth=auth, verify=False, timeout=30)

        if response.status_code == 201:
            new_category = response.json()
            print(f"成功创建分类: {category_name} (ID: {new_category['id']})")
            return new_category['id']
        else:
            print(f"创建分类失败: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        print(f"获取/创建分类时出错: {e}")
        return None

def publish_to_wordpress(site_url, title, content, category_id):
    """
    发布文章到WordPress
    """
    posts_url = f"{site_url}/wp-json/wp/v2/posts"

    post_data = {
        'title': title,
        'content': content,
        'status': 'publish',
        'categories': [category_id] if category_id else []
    }

    auth = (_WP_USER, _WP_API_KEY.replace(' ', ''))

    try:
        # 添加SSL验证和超时设置
        response = requests.post(posts_url, json=post_data, auth=auth, verify=False, timeout=60)

        if response.status_code == 201:
            post_info = response.json()
            return post_info['id'], post_info['link']
        else:
            print(f"发布失败: {response.status_code} - {response.text}")
            return None, None

    except Exception as e:
        print(f"发布文章时出错: {e}")
        return None, None

def main():
    """
    主函数：生成并发布12生肖今日运势
    """
    import sys

    # 获取今日日期
    today = datetime.now()
    date_str = today.strftime("%Y年%m月%d日")

    # 检查命令行参数
    target_zodiacs = ZODIAC_ANIMALS
    if len(sys.argv) > 1:
        specified_zodiac = sys.argv[1]
        if specified_zodiac in ZODIAC_ANIMALS:
            target_zodiacs = [specified_zodiac]
            print(f"只生成指定生肖：{specified_zodiac}")
        else:
            print(f"错误：未知的生肖 '{specified_zodiac}'")
            print(f"可用的生肖：{', '.join(ZODIAC_ANIMALS)}")
            return

    print(f"开始生成{date_str}的生肖运势...")
    print(f"目标生肖：{', '.join(target_zodiacs)}")

    # 获取www站点URL
    www_site = sites.get('www')
    if not www_site:
        print("错误：未找到www站点配置")
        return

    print(f"目标站点：{www_site}")

    # 获取或创建"生肖今日运势"分类
    category_id = get_category_id(www_site, "生肖今日运势")
    if not category_id:
        print("警告：无法获取分类ID，将不设置分类")

    success_count = 0

    # 为每个生肖生成并发布文章
    for zodiac in target_zodiacs:
        print(f"\n正在处理生肖：{zodiac}")

        # 生成运势内容
        print(f"  正在生成{zodiac}运势内容...")
        content = generate_fortune_content(date_str, zodiac)
        if not content:
            print(f"  ❌ 生成{zodiac}运势失败，跳过")
            continue

        print(f"  ✅ {zodiac}运势内容生成成功")

        # 提取标题（假设第一行是标题）
        lines = content.split('\n')
        title = lines[0].strip('「」')

        # 发布到WordPress
        print(f"  正在发布{zodiac}运势到WordPress...")
        post_id, post_url = publish_to_wordpress(www_site, title, content, category_id)

        if post_id:
            print(f"  ✅ {zodiac}运势发布成功")
            print(f"     文章ID: {post_id}")
            print(f"     文章链接: {post_url}")
            success_count += 1
        else:
            print(f"  ❌ {zodiac}运势发布失败")

    print(f"\n任务完成！成功发布 {success_count}/{len(target_zodiacs)} 篇文章")

if __name__ == "__main__":
    print("脚本开始执行...")
    try:
        main()
    except Exception as e:
        print(f"脚本执行出错: {e}")
        import traceback
        traceback.print_exc()
