#!/usr/bin/env python3
"""
设置系统服务脚本
用于在系统启动时自动运行调度器
"""

import os
import sys
import subprocess
from pathlib import Path

def create_systemd_service():
    """创建systemd服务文件（Linux）"""
    current_dir = Path(__file__).parent.absolute()
    python_path = sys.executable
    
    service_content = f"""[Unit]
Description=生肖运势自动生成器调度服务
After=network.target

[Service]
Type=simple
User={os.getenv('USER', 'root')}
WorkingDirectory={current_dir}
ExecStart={python_path} {current_dir}/scheduler.py start
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"""
    
    service_file = "/etc/systemd/system/zodiac-scheduler.service"
    
    print("创建systemd服务文件...")
    print(f"服务文件路径: {service_file}")
    print("需要管理员权限，请输入密码:")
    
    try:
        # 写入服务文件
        with open("/tmp/zodiac-scheduler.service", "w") as f:
            f.write(service_content)
        
        # 移动到系统目录
        subprocess.run(["sudo", "mv", "/tmp/zodiac-scheduler.service", service_file], check=True)
        
        # 重新加载systemd
        subprocess.run(["sudo", "systemctl", "daemon-reload"], check=True)
        
        # 启用服务
        subprocess.run(["sudo", "systemctl", "enable", "zodiac-scheduler"], check=True)
        
        print("✅ systemd服务创建成功！")
        print("\n管理命令:")
        print("启动服务: sudo systemctl start zodiac-scheduler")
        print("停止服务: sudo systemctl stop zodiac-scheduler")
        print("查看状态: sudo systemctl status zodiac-scheduler")
        print("查看日志: sudo journalctl -u zodiac-scheduler -f")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 创建服务失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def create_launchd_plist():
    """创建launchd配置文件（macOS）"""
    current_dir = Path(__file__).parent.absolute()
    python_path = sys.executable
    user = os.getenv('USER')
    
    plist_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.zodiac.scheduler</string>
    <key>ProgramArguments</key>
    <array>
        <string>{python_path}</string>
        <string>{current_dir}/scheduler.py</string>
        <string>start</string>
    </array>
    <key>WorkingDirectory</key>
    <string>{current_dir}</string>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>{current_dir}/scheduler.log</string>
    <key>StandardErrorPath</key>
    <string>{current_dir}/scheduler.error.log</string>
</dict>
</plist>
"""
    
    plist_file = f"/Users/<USER>/Library/LaunchAgents/com.zodiac.scheduler.plist"
    
    print("创建launchd配置文件...")
    print(f"配置文件路径: {plist_file}")
    
    try:
        # 确保目录存在
        os.makedirs(f"/Users/<USER>/Library/LaunchAgents", exist_ok=True)
        
        # 写入配置文件
        with open(plist_file, "w") as f:
            f.write(plist_content)
        
        # 加载服务
        subprocess.run(["launchctl", "load", plist_file], check=True)
        
        print("✅ launchd服务创建成功！")
        print("\n管理命令:")
        print(f"启动服务: launchctl start com.zodiac.scheduler")
        print(f"停止服务: launchctl stop com.zodiac.scheduler")
        print(f"卸载服务: launchctl unload {plist_file}")
        print(f"查看日志: tail -f {current_dir}/scheduler.log")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 创建服务失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def create_startup_script():
    """创建启动脚本"""
    current_dir = Path(__file__).parent.absolute()
    python_path = sys.executable
    
    script_content = f"""#!/bin/bash
# 生肖运势调度器启动脚本

cd {current_dir}
{python_path} scheduler.py start
"""
    
    script_file = current_dir / "start_scheduler.sh"
    
    try:
        with open(script_file, "w") as f:
            f.write(script_content)
        
        # 添加执行权限
        os.chmod(script_file, 0o755)
        
        print(f"✅ 启动脚本创建成功: {script_file}")
        print(f"手动启动: {script_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 生肖运势调度器服务安装程序")
    print("=" * 50)
    
    # 检测操作系统
    import platform
    system = platform.system()
    
    print(f"检测到操作系统: {system}")
    
    if system == "Linux":
        print("\n选择安装方式:")
        print("1. 安装为systemd服务（推荐）")
        print("2. 仅创建启动脚本")
        
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            success = create_systemd_service()
        else:
            success = create_startup_script()
            
    elif system == "Darwin":  # macOS
        print("\n选择安装方式:")
        print("1. 安装为launchd服务（推荐）")
        print("2. 仅创建启动脚本")
        
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            success = create_launchd_plist()
        else:
            success = create_startup_script()
            
    else:
        print("⚠️ 不支持的操作系统，仅创建启动脚本")
        success = create_startup_script()
    
    if success:
        print("\n🎉 安装完成！")
        print("\n📋 调度任务:")
        print("• 每天08:00自动生成12生肖运势")
        print("• 自动发布到https://yi958.com")
        print("\n📝 日志文件:")
        print("• scheduler.log - 调度器日志")
        print("• scheduler.error.log - 错误日志")
    else:
        print("\n❌ 安装失败，请检查错误信息")

if __name__ == "__main__":
    main()
