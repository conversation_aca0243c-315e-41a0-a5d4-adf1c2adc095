#!/usr/bin/env python3
"""
测试生肖运势生成功能
"""

from datetime import datetime
from openai import OpenAI

# OpenAI 配置
OPENAI_API_KEY = "sk-Abwb3yNpXXbJiMFmD3Ca2a3b12C846AdAaF2C5A0Bc16Ef08"
OPENAI_BASE_URL = "https://api.leaflow.net/v1"  # 添加 /v1 路径
MODEL = "gpt-4o-mini"

# 初始化OpenAI客户端
client = OpenAI(
    api_key=OPENAI_API_KEY,
    base_url=OPENAI_BASE_URL
)

def generate_fortune_content(date_str, zodiac):
    """
    使用OpenAI生成指定生肖的运势内容
    """
    prompt = f"""你是一位资深命理专家，请根据给定的日期和生肖生成今日运势报告。

请只输出文章正文部分，不要包含标题和导语。

运势分类（每类2-3句话）：
今日爱情运：[生成描述]
今日事业运：[生成描述]
今日财运：[生成描述]
今日人际运：[生成描述]

开运指南（严格按顺序）：
开运数字：[主推2个数字]；最佳，其次[补充2个数字]
开运颜色：[2种颜色]
开运建议：[1-2句指导性话语，包含"学会"等动词]

生成规则：
- 日期格式：YYYY年MM月DD日
- 用口语化中文，避免极端负面词汇
- 财运描述需包含风险提示
- 开运数字颜色需符合生肖五行
- 爱情运需区分未婚/已婚状态
- 事业运要包含具体建议

现在请为日期={date_str} 生肖={zodiac} 生成运势正文内容。只输出正文，不要标题和导语。"""

    try:
        response = client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=1500
        )

        # 获取生成的正文内容
        content = response.choices[0].message.content.strip()

        # 构建完整的文章内容
        title = f"生肖属{zodiac}人今日运势运程财运吉凶（{date_str}）详解查询"

        intro = f"以下是生肖属{zodiac}的人{date_str}的运势预测。易德轩提醒您，记得每天来查看十二生肖今日运程每日运势，大师帮你分析事业、爱情、财运，助您掌握自身运势每日过得精彩。请收藏[易德轩每日十二生肖属相运程]为书签网址，方便下次点击即可直达。"

        ending = """以上是根据你今天的生肖运势所提供的今日幸运颜色、数字、贵人等开运方法，可以让你的自信心加倍，好运能量上升！
　　生肖，即十二属相，指不同年份出生的人，具有先天不同的属相命运，十二年一个轮回，此为"命"也。属相的先天命运，随着日月轮回的变化，每天的运势也不一样，此为"运"也，所谓"命运"，就是指先天的命相和后天的运程。
　　十二生肖每日运势在线查询，即根据生肖五行相生相克、生肖地支之间相刑相冲以及命理神煞学，来解析十二属相的每日运势，旨在为您今天的运势提供一些参考。此外，我们结合生肖运势，提供了一些开运建议。"""

        # 组合完整内容
        full_content = f"「{title}」\n\n{intro}\n\n{content}\n\n{ending}"

        return full_content

    except Exception as e:
        print(f"生成{zodiac}运势时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """
    测试生成牛的运势
    """
    # 使用指定日期
    date_str = "2025年07月03日"
    zodiac = "牛"
    
    print(f"正在生成{date_str}生肖{zodiac}的运势...")
    
    content = generate_fortune_content(date_str, zodiac)
    
    if content:
        print("\n生成成功！内容如下：")
        print("=" * 50)
        print(content)
        print("=" * 50)
    else:
        print("生成失败！")

if __name__ == "__main__":
    main()
