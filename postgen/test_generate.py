#!/usr/bin/env python3
"""
测试生肖运势生成功能
"""

from datetime import datetime
from config import get_config, get_openai_client

# 获取配置实例
config = get_config()

# 获取OpenAI客户端
client = get_openai_client()

def generate_fortune_content(date_str, zodiac):
    """
    使用OpenAI生成指定生肖的运势内容
    """
    # 获取提示词模板
    prompt_template = config.get_prompt_template()
    prompt = prompt_template.format(date=date_str, zodiac=zodiac)
    
    # 获取OpenAI配置
    openai_config = config.get_openai_config()

    try:
        response = client.chat.completions.create(
            model=openai_config["model"],
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=openai_config["temperature"],
            max_tokens=openai_config["max_tokens"]
        )
        
        # 获取生成的正文内容
        content = response.choices[0].message.content.strip()
        
        # 使用配置中的模板格式化完整内容
        full_content = config.format_article_content(zodiac, date_str, content)
        
        return full_content
    
    except Exception as e:
        print(f"生成{zodiac}运势时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """
    测试生成牛的运势
    """
    # 使用指定日期
    date_str = "2025年07月03日"
    zodiac = "牛"
    
    print(f"正在生成{date_str}生肖{zodiac}的运势...")
    
    content = generate_fortune_content(date_str, zodiac)
    
    if content:
        print("\n生成成功！内容如下：")
        print("=" * 50)
        print(content)
        print("=" * 50)
    else:
        print("生成失败！")

if __name__ == "__main__":
    main()
