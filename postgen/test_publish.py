#!/usr/bin/env python3
"""
测试WordPress发布功能
"""

import requests
from datetime import datetime
from openai import OpenAI
from sites import sites, _WP_API_KEY, _WP_USER

# OpenAI 配置
OPENAI_API_KEY = "sk-Abwb3yNpXXbJiMFmD3Ca2a3b12C846AdAaF2C5A0Bc16Ef08"
OPENAI_BASE_URL = "https://api.leaflow.net/v1"
MODEL = "gpt-4o-mini"

# 初始化OpenAI客户端
client = OpenAI(
    api_key=OPENAI_API_KEY,
    base_url=OPENAI_BASE_URL
)

def generate_fortune_content(date_str, zodiac):
    """
    使用OpenAI生成指定生肖的运势内容
    """
    prompt = f"""你是一位资深命理专家，请根据给定的日期和生肖生成今日运势报告。要求如下：

标题格式： 「生肖属{zodiac}人今日运势运程财运吉凶（{date_str}）详解查询」

固定导语： 「以下是生肖属{zodiac}的人{date_str}的运势预测。易德轩提醒您，记得每天来查看十二生肖今日运程每日运势，大师帮你分析事业、爱情、财运，助您掌握自身运势每日过得精彩。请收藏[易德轩每日十二生肖属相运程]为书签网址，方便下次点击即可直达。」

运势分类（每类2-3句话）： 
今日爱情运：[生成描述] 
今日事业运：[生成描述] 
今日财运：[生成描述] 
今日人际运：[生成描述]

开运指南（严格按顺序）： 
开运数字：[主推2个数字]；最佳，其次[补充2个数字] 
开运颜色：[2种颜色] 
开运建议：[1-2句指导性话语，包含"学会"等动词]

生成规则：
- 日期格式：YYYY年MM月DD日
- 用口语化中文，避免极端负面词汇
- 财运描述需包含风险提示
- 开运数字颜色需符合生肖五行
- 爱情运需区分未婚/已婚状态
- 事业运要包含具体建议

现在请为日期={date_str} 生肖={zodiac} 生成运势。请直接输出完整的文章内容，不要包含任何额外的说明。"""

    try:
        response = client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=1500
        )
        
        return response.choices[0].message.content.strip()
    
    except Exception as e:
        print(f"生成{zodiac}运势时出错: {e}")
        return None

def get_category_id(site_url, category_name):
    """
    获取WordPress分类ID，如果不存在则创建
    """
    # 获取现有分类
    categories_url = f"{site_url}/wp-json/wp/v2/categories"
    
    try:
        response = requests.get(categories_url)
        print(f"获取分类响应状态: {response.status_code}")
        
        if response.status_code == 200:
            categories = response.json()
            print(f"找到 {len(categories)} 个分类")
            for category in categories:
                if category['name'] == category_name:
                    print(f"找到现有分类: {category_name} (ID: {category['id']})")
                    return category['id']
        
        # 如果分类不存在，创建新分类
        print(f"分类 '{category_name}' 不存在，正在创建...")
        create_data = {
            'name': category_name,
            'description': f'{category_name}相关文章'
        }
        
        auth = (_WP_USER, _WP_API_KEY.replace(' ', ''))
        response = requests.post(categories_url, json=create_data, auth=auth)
        
        print(f"创建分类响应状态: {response.status_code}")
        if response.status_code == 201:
            new_category = response.json()
            print(f"成功创建分类: {category_name} (ID: {new_category['id']})")
            return new_category['id']
        else:
            print(f"创建分类失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"获取/创建分类时出错: {e}")
        return None

def publish_to_wordpress(site_url, title, content, category_id):
    """
    发布文章到WordPress
    """
    posts_url = f"{site_url}/wp-json/wp/v2/posts"
    
    post_data = {
        'title': title,
        'content': content,
        'status': 'publish',
        'categories': [category_id] if category_id else []
    }
    
    auth = (_WP_USER, _WP_API_KEY.replace(' ', ''))
    
    try:
        print(f"正在发布文章到: {posts_url}")
        print(f"文章标题: {title}")
        print(f"分类ID: {category_id}")
        
        response = requests.post(posts_url, json=post_data, auth=auth)
        
        print(f"发布响应状态: {response.status_code}")
        if response.status_code == 201:
            post_info = response.json()
            return post_info['id'], post_info['link']
        else:
            print(f"发布失败: {response.status_code} - {response.text}")
            return None, None
            
    except Exception as e:
        print(f"发布文章时出错: {e}")
        return None, None

def main():
    """
    测试生成并发布一篇牛的运势
    """
    # 使用指定日期
    date_str = "2025年07月03日"
    zodiac = "牛"
    
    print(f"开始测试生成并发布{date_str}生肖{zodiac}的运势...")
    
    # 获取www站点URL
    www_site = sites.get('www')
    if not www_site:
        print("错误：未找到www站点配置")
        return
    
    print(f"目标站点: {www_site}")
    
    # 获取或创建"生肖今日运势"分类
    category_id = get_category_id(www_site, "生肖今日运势")
    if not category_id:
        print("警告：无法获取分类ID，将不设置分类")
    
    # 生成运势内容
    print(f"\n正在生成{zodiac}运势内容...")
    content = generate_fortune_content(date_str, zodiac)
    if not content:
        print(f"生成{zodiac}运势失败")
        return
    
    print("运势内容生成成功！")
    
    # 提取标题（假设第一行是标题）
    lines = content.split('\n')
    title = lines[0].strip('「」')
    
    print(f"提取的标题: {title}")
    
    # 发布到WordPress
    print(f"\n正在发布文章...")
    post_id, post_url = publish_to_wordpress(www_site, title, content, category_id)
    
    if post_id:
        print(f"✅ {zodiac}运势发布成功")
        print(f"   文章ID: {post_id}")
        print(f"   文章链接: {post_url}")
    else:
        print(f"❌ {zodiac}运势发布失败")

if __name__ == "__main__":
    main()
