#!/usr/bin/env python3
"""
测试WordPress发布功能
"""

import requests
from datetime import datetime
from config import get_config, get_openai_client

# 获取配置实例
config = get_config()

# 获取OpenAI客户端
client = get_openai_client()

def generate_fortune_content(date_str, zodiac):
    """
    使用OpenAI生成指定生肖的运势内容
    """
    # 获取提示词模板
    prompt_template = config.get_prompt_template()
    prompt = prompt_template.format(date=date_str, zodiac=zodiac)
    
    # 获取OpenAI配置
    openai_config = config.get_openai_config()

    try:
        response = client.chat.completions.create(
            model=openai_config["model"],
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=openai_config["temperature"],
            max_tokens=openai_config["max_tokens"]
        )
        
        # 获取生成的正文内容
        content = response.choices[0].message.content.strip()
        
        # 使用配置中的模板格式化完整内容
        full_content = config.format_article_content(zodiac, date_str, content)
        
        return full_content
    
    except Exception as e:
        print(f"生成{zodiac}运势时出错: {e}")
        return None

def get_category_id(site_url, category_name):
    """
    获取WordPress分类ID，如果不存在则创建
    """
    # 获取现有分类
    categories_url = f"{site_url}/wp-json/wp/v2/categories"
    
    # 获取WordPress配置
    wp_config = config.get_wordpress_config()
    auth = config.get_wordpress_auth()
    
    try:
        response = requests.get(
            categories_url, 
            verify=wp_config["verify_ssl"], 
            timeout=wp_config["timeout"]
        )
        print(f"获取分类响应状态: {response.status_code}")
        
        if response.status_code == 200:
            categories = response.json()
            print(f"找到 {len(categories)} 个分类")
            for category in categories:
                if category['name'] == category_name:
                    print(f"找到现有分类: {category_name} (ID: {category['id']})")
                    return category['id']
        
        # 如果分类不存在，创建新分类
        print(f"分类 '{category_name}' 不存在，正在创建...")
        create_data = {
            'name': category_name,
            'description': f'{category_name}相关文章'
        }
        
        response = requests.post(
            categories_url, 
            json=create_data, 
            auth=auth, 
            verify=wp_config["verify_ssl"], 
            timeout=wp_config["timeout"]
        )
        
        print(f"创建分类响应状态: {response.status_code}")
        if response.status_code == 201:
            new_category = response.json()
            print(f"成功创建分类: {category_name} (ID: {new_category['id']})")
            return new_category['id']
        else:
            print(f"创建分类失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"获取/创建分类时出错: {e}")
        return None

def publish_to_wordpress(site_url, title, content, category_id):
    """
    发布文章到WordPress
    """
    posts_url = f"{site_url}/wp-json/wp/v2/posts"
    
    post_data = {
        'title': title,
        'content': content,
        'status': 'publish',
        'categories': [category_id] if category_id else []
    }
    
    # 获取WordPress配置和认证信息
    wp_config = config.get_wordpress_config()
    auth = config.get_wordpress_auth()
    
    try:
        print(f"正在发布文章到: {posts_url}")
        print(f"文章标题: {title}")
        print(f"分类ID: {category_id}")
        
        response = requests.post(
            posts_url, 
            json=post_data, 
            auth=auth, 
            verify=wp_config["verify_ssl"], 
            timeout=wp_config["timeout"]
        )
        
        print(f"发布响应状态: {response.status_code}")
        if response.status_code == 201:
            post_info = response.json()
            return post_info['id'], post_info['link']
        else:
            print(f"发布失败: {response.status_code} - {response.text}")
            return None, None
            
    except Exception as e:
        print(f"发布文章时出错: {e}")
        return None, None

def main():
    """
    测试生成并发布一篇牛的运势
    """
    # 使用指定日期
    date_str = "2025年07月03日"
    zodiac = "牛"
    
    print(f"开始测试生成并发布{date_str}生肖{zodiac}的运势...")
    
    # 获取www站点URL
    www_site = config.get_wordpress_site_url('www')
    if not www_site:
        print("错误：未找到www站点配置")
        return
    
    print(f"目标站点: {www_site}")
    
    # 获取或创建"生肖今日运势"分类
    wp_config = config.get_wordpress_config()
    category_id = get_category_id(www_site, wp_config["category_name"])
    if not category_id:
        print("警告：无法获取分类ID，将不设置分类")
    
    # 生成运势内容
    print(f"\n正在生成{zodiac}运势内容...")
    content = generate_fortune_content(date_str, zodiac)
    if not content:
        print(f"生成{zodiac}运势失败")
        return
    
    print("运势内容生成成功！")
    
    # 提取标题（假设第一行是标题）
    lines = content.split('\n')
    title = lines[0].strip('「」')
    
    print(f"提取的标题: {title}")
    
    # 发布到WordPress
    print(f"\n正在发布文章...")
    post_id, post_url = publish_to_wordpress(www_site, title, content, category_id)
    
    if post_id:
        print(f"✅ {zodiac}运势发布成功")
        print(f"   文章ID: {post_id}")
        print(f"   文章链接: {post_url}")
    else:
        print(f"❌ {zodiac}运势发布失败")

if __name__ == "__main__":
    main()
