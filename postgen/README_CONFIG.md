# 🔧 配置管理系统

## 概述

项目现已采用统一的配置管理系统，所有配置信息集中在 `config.py` 中，使用单例模式确保配置的一致性和线程安全。

## 🏗️ 架构设计

### 单例模式配置类
```python
from config import get_config, get_openai_client

# 获取配置实例
config = get_config()

# 获取OpenAI客户端（单例）
client = get_openai_client()
```

### 配置分类
- **OpenAI配置**: API密钥、模型、参数
- **WordPress配置**: 认证、站点、分类
- **生肖配置**: 生肖列表、处理参数
- **调度器配置**: 时间、日志、超时
- **内容配置**: 模板、格式

## 🛠️ 配置管理

### 查看配置
```bash
python config_manager.py
# 选择 "1. 查看当前配置"
```

### 测试连接
```bash
python config_manager.py
# 选择 "2. 测试OpenAI连接"
# 选择 "3. 测试WordPress连接"
```

### 修改配置
```bash
python config_manager.py
# 选择 "4. 更新调度时间"
# 选择 "5. 添加WordPress站点"
```

## 📊 当前配置

### OpenAI API
- **Base URL**: `https://api.leaflow.net/v1`
- **模型**: `gpt-4o-mini`
- **温度**: `0.7`
- **最大Token**: `1500`

### WordPress
- **主站点**: `https://yi958.com`
- **分类**: "生肖今日运势"
- **SSL验证**: `False`
- **超时**: `60秒`

### 调度器
- **执行时间**: `08:00`
- **任务超时**: `3600秒`
- **日志文件**: `scheduler.log`

## 🔄 配置更新

### 程序化更新
```python
from config import get_config

config = get_config()

# 更新OpenAI配置
config.update_openai_config(temperature=0.8)

# 更新WordPress配置
config.update_wordpress_config(timeout=120)

# 添加新站点
config.add_wordpress_site("new_site", "https://new.example.com")

# 更新调度时间
config.update_scheduler_time("zodiac_fortune", "09:00")
```

### 配置导出
```bash
python config_manager.py
# 选择 "6. 导出配置"
# 生成 config_backup.json
```

## 🔒 线程安全

配置系统使用线程锁确保在多线程环境下的安全性：

- **配置实例**: 单例模式，线程安全
- **OpenAI客户端**: 延迟初始化，线程安全
- **配置更新**: 原子操作，避免竞态条件

## 🎯 优势

1. **集中管理**: 所有配置统一管理
2. **类型安全**: 强类型配置项
3. **单例模式**: 确保配置一致性
4. **线程安全**: 支持并发访问
5. **易于扩展**: 简单添加新配置项
6. **配置验证**: 自动验证配置有效性

## 📝 使用示例

### 基本使用
```python
from config import get_config, get_openai_client

# 获取配置
config = get_config()
openai_config = config.get_openai_config()
wp_sites = config.get_wordpress_sites()

# 使用OpenAI客户端
client = get_openai_client()
response = client.chat.completions.create(...)

# 格式化内容
content = config.format_article_content("牛", "2025年07月03日", "正文内容")
```

### 高级配置
```python
# 获取特定配置
zodiac_animals = config.get_zodiac_animals()
wp_auth = config.get_wordpress_auth()
prompt_template = config.get_prompt_template()

# 动态配置
site_url = config.get_wordpress_site_url('www')
category_name = config.get_wordpress_config()["category_name"]
```

## 🔧 维护

### 配置备份
定期导出配置文件作为备份：
```bash
python config_manager.py  # 选择导出配置
```

### 配置验证
使用测试功能验证配置正确性：
```bash
python config_manager.py  # 测试连接功能
```

### 配置监控
通过日志监控配置使用情况：
```bash
tail -f scheduler.log
```
